import type Echo from 'laravel-echo'
import type {PresenceChannel} from "laravel-echo/src/channel";
import { getCachedWebRTCCredentials } from './webrtc-utils';

declare global {
    interface Window {
        Pusher: any;
        Echo: Echo;
    }
}

type EchoSimplePeerMode = 'controller' | 'gamer';
type USER = { userId: number, name: string, anon: boolean }
type ExpandedSignalData = { from: number } & ({
    candidate: RTCIceCandidate
} | {
    answer: any
} | {
    offer: any
})

// Dynamic ICE servers will be fetched from backend
let dynamicIceServers: RTCIceServer[] = [];

export class EchoWebRtc {
    private controlPeer!: RTCPeerConnection;
    private privateEcho: PresenceChannel;
    private dataChannel!: RTCDataChannel;
    public onStream!: (streams: readonly MediaStream[]) => void;
    public onPeerLeaving!: () => void;
    private dataChannelListener?: (data: MessageEvent<any>) => void

    constructor(private mode: EchoSimplePeerMode,
                private userId: number,
                private machineId: string,
                private streams?: MediaStream[]) {
        this.privateEcho = window.Echo.join(`machineControl.${machineId}`)
        this.privateEcho.listenForWhisper('signal', this.listenControllerGamerSignalWhisper.bind(this))
        this.privateEcho.here(this.gamerControllerHere.bind(this))
            .joining(this.gamerControllerJoining.bind(this))
            .leaving(this.gamerControllerLeaving.bind(this))
            .error(this.gamerControllerError.bind(this));
        this.privateEcho.whisper('signal', {'ping': 'pong'})

        // Initialize dynamic ICE servers
        this.initializeDynamicCredentials();
    }

    private async initializeDynamicCredentials() {
        try {
            console.log('[EchoWebRtc] Initializing dynamic WebRTC credentials...');
            dynamicIceServers = await getCachedWebRTCCredentials();
            console.log('[EchoWebRtc] Dynamic credentials initialized successfully');
        } catch (error) {
            console.error('[EchoWebRtc] Failed to initialize dynamic credentials:', error);
        }
    }

    private async gamerControllerHere(users: USER[]) {
        console.log('here', users)
        this.controlPeer = await this.createControlPeer();
        if (this.mode === 'controller') {
            if (users.length === 2) {
                this.startOffer(this.controlPeer, (d) => this.privateSend('signal', d))
            } else {
                console.log('joined empty control lobby')
            }
        }
    }

    private async replaceTrack(newStream: MediaStream) {
        this.streams = [newStream]
        for (const s of this.controlPeer.getSenders()) {
            let videoTrack = newStream.getVideoTracks()[0];
            if (s.track !== videoTrack) {
                await s.replaceTrack(videoTrack)
            }
        }
    }

    private async gamerControllerJoining(user: USER) {
        console.log('joining', user)
        if (this.controlPeer == null) {
            this.controlPeer = await this.createControlPeer();
        }
        if (this.mode === 'controller') {
            this.startOffer(this.controlPeer, (d) => this.privateSend('signal', d))
        }
    }

    private async gamerControllerLeaving(user: USER) {
        console.log('leaving', user)
        this.controlPeer.close()
        this.controlPeer = await this.createControlPeer()
        this.onPeerLeaving?.apply(this);
    }

    private gamerControllerError(error: unknown) {
        console.error('gamerControllerError', error)
    }

    private async createControlPeer() {
        // Ensure we have dynamic credentials before creating peer connection
        if (dynamicIceServers.length === 0) {
            console.log('[EchoWebRtc] Dynamic credentials not ready, fetching...');
            dynamicIceServers = await getCachedWebRTCCredentials();
        }

        console.log('[EchoWebRtc] Creating peer connection with ICE servers:', dynamicIceServers);
        let peer = new RTCPeerConnection({
            iceServers: dynamicIceServers
        });
        peer.onicecandidateerror = (conn,err) => console.log('error', err)
        peer.onicecandidate = this.privateGotLocalIceCandidateOffer.bind(this);
        if (this.mode === 'controller'){
            this.dataChannel = peer.createDataChannel('MachineChannel');
            this.dataChannel.onopen = event => {
                console.log('open data channel',event)
            }
            this.setupDataChannel();
        } else {
            peer.ondatachannel = (event: RTCDataChannelEvent) => {
                console.log('recieved data channel')
                this.dataChannel = event.channel
                this.setupDataChannel()
                this.send({type: 'RESET'})
            }
        }
        peer.ontrack = this.privateGotTrack.bind(this);
        this.streams?.forEach(s => {
            s.getTracks().forEach((track) => peer.addTrack(track, s))
        })
        // this.stream?.getTracks().forEach((track) => peer.addTrack(track, this.stream!))
        return peer;
    }

    private setupDataChannel() {
        this.dataChannel.onmessage = this.onDataChannelMessage.bind(this);
    }

    private onDataChannelMessage(message: MessageEvent) {
        console.log('got data channel message',message)
        if (this.dataChannelListener){
            this.dataChannelListener(message);
        }
    }

    public setDataChannelListener(func: (arg: MessageEvent) => void) {
        this.dataChannelListener = func;
    }

    public send(message: any) {
        if (typeof message !== 'string') {
            message = JSON.stringify(message)
        }
        this.dataChannel.send(message)
    }


    private privateGotLocalIceCandidateOffer(event: RTCPeerConnectionIceEvent) {
        console.log('got private ice candidate offer', event)
        if ('candidate' in event && event.candidate) {
            console.log('sending private ice candidate', event)
            this.privateSend('signal', {candidate: event.candidate, from: this.userId})
        }
    }

    private privateGotTrack(event: RTCTrackEvent) {
        this.onStream(event.streams);
    }

    private privateSend(event: 'signal', data: ExpandedSignalData) {
        console.log('sending private signal', data)
        this.privateEcho.whisper(event, data)
    }

    private async listenControllerGamerSignalWhisper(data: ExpandedSignalData) {
        console.log('receiving private signal', data)
        if ('offer' in data) {
            await this.controlPeer.setRemoteDescription(new RTCSessionDescription(data.offer));
            const answer = await this.controlPeer.createAnswer();
            await this.controlPeer.setLocalDescription(answer);
            this.privateSend('signal', {answer, from: this.userId})
        } else if ('answer' in data) {
            await this.controlPeer.setRemoteDescription(new RTCSessionDescription(data.answer))
        } else if ('candidate' in data && data.candidate && this.controlPeer.remoteDescription) {
            await this.controlPeer.addIceCandidate(new RTCIceCandidate(data.candidate))
        }
    }

    private async startOffer(peer: RTCPeerConnection, send: (d: { offer: any, from: number }) => void) {
        console.log('making initial offer')
        const offer = await peer.createOffer();
        await peer.setLocalDescription(offer)
        send({offer, from: this.userId})
    }

    public leaving() {
        window.Echo.leave(`machineControl.${this.machineId}`)
    }
}
