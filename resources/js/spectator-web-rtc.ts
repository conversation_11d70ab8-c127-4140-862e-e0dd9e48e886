import type Echo from 'laravel-echo'
import type {PresenceChannel} from "laravel-echo/src/channel";
import { getCachedWebRTCCredentials } from './webrtc-utils';

declare global {
    interface Window {
        Pusher: any;
        Echo: Echo;
    }
}

type SpectatorSimplePeerMode = 'controller' | 'spectator';
type USER = { userId: number | string, name: string, anon: boolean }
type ExpandedSignalData = { from: number | string } & ({
    candidate: RTCIceCandidate
} | {
    answer: any
} | {
    offer: any
})
// Dynamic ICE servers will be fetched from backend
let dynamicIceServers: RTCIceServer[] = [];

export class SpectatorWebRtc {
    private spectatorPeer: null|RTCPeerConnection = null;
    private controllerSpectatorPeers: Record<number | string, RTCPeerConnection> = {}
    private spectators: Record<number | string, USER> = {};
    private spectatorEcho: PresenceChannel;
    private debugPrefix: string;

    constructor(private mode: SpectatorSimplePeerMode,
                private userId: number | string,
                private machineId: string,
                private stream?: MediaStream,
                private videoElement?: HTMLVideoElement) {

        this.debugPrefix = `[SpectatorWebRTC:${mode}:${userId}]`;

        console.group(`${this.debugPrefix} INITIALIZATION`);
        console.log(`${this.debugPrefix} Constructor called with:`, {
            mode,
            userId,
            machineId,
            hasStream: !!stream,
            streamTracks: stream?.getTracks().length || 0,
            hasVideoElement: !!videoElement,
            videoElementId: videoElement?.id || 'no-id'
        });

        if (stream) {
            console.log(`${this.debugPrefix} Stream details:`, {
                id: stream.id,
                active: stream.active,
                tracks: stream.getTracks().map(t => ({
                    kind: t.kind,
                    id: t.id,
                    enabled: t.enabled,
                    readyState: t.readyState
                }))
            });
        }

        console.log(`${this.debugPrefix} Joining presence channel: machineSpectator.${machineId}`);
        this.spectatorEcho = window.Echo.join(`machineSpectator.${machineId}`)

        console.log(`${this.debugPrefix} Setting up presence channel event handlers...`);
        this.spectatorEcho.here(this.spectatorControllerHere.bind(this))
            .joining(this.spectatorControllerJoining.bind(this))
            .leaving(this.spectatorControllerLeaving.bind(this))
            .error(this.spectatorControllerError.bind(this));

        console.log(`${this.debugPrefix} Setting up whisper listeners for mode: ${mode}`);
        if (mode === 'controller') {
            console.log(`${this.debugPrefix} Controller mode: listening for 'signal' whispers`);
            this.spectatorEcho.listenForWhisper(`signal`, this.listenControllerMachineSignalWhisper.bind(this));
            this.spectatorEcho.whisper(`signal`, {'ping': 'pong', from: userId, timestamp: Date.now()})
        } else {
            console.log(`${this.debugPrefix} Spectator mode: listening for 'signal.${userId}' whispers`);
            this.spectatorEcho.listenForWhisper(`signal.${userId}`, this.listenControllerSpectatorSignalWhisper.bind(this));
            this.spectatorEcho.whisper(`signal.${userId}`, {'ping': 'pong', from: userId, timestamp: Date.now()})
        }

        // Initialize dynamic ICE servers
        this.initializeDynamicCredentials();

        console.log(`${this.debugPrefix} Initialization complete`);
        console.groupEnd();
    }

    private async initializeDynamicCredentials() {
        try {
            console.log(`${this.debugPrefix} Initializing dynamic WebRTC credentials...`);
            dynamicIceServers = await getCachedWebRTCCredentials();
            console.log(`${this.debugPrefix} Dynamic credentials initialized successfully`);
        } catch (error) {
            console.error(`${this.debugPrefix} Failed to initialize dynamic credentials:`, error);
        }
    }

    private async spectatorControllerHere(users: USER[]) {
        console.group(`${this.debugPrefix} PRESENCE CHANNEL - HERE EVENT`);
        console.log(`${this.debugPrefix} Raw users data:`, users);
        console.log(`${this.debugPrefix} Users type: ${typeof users}, Array: ${Array.isArray(users)}, Length: ${users?.length || 0}`);

        if (!users || !Array.isArray(users)) {
            console.error(`${this.debugPrefix} Invalid users data received in 'here' event`);
            console.groupEnd();
            return;
        }

        console.log(`${this.debugPrefix} Processing ${users.length} users in channel`);

        // Log each user in detail
        users.forEach((user, index) => {
            console.log(`${this.debugPrefix} User ${index + 1}:`, {
                userId: user.userId,
                name: user.name,
                anon: user.anon,
                isSelf: user.userId === this.userId,
                keys: Object.keys(user)
            });
        });

        // Build spectators list (excluding self)
        let spectatorCount = 0;
        for (let user of users) {
            if (user.userId === this.userId) {
                console.log(`${this.debugPrefix} Skipping self (${user.userId})`);
                continue;
            }
            this.spectators[user.userId] = user;
            spectatorCount++;
            console.log(`${this.debugPrefix} Added spectator: ${user.name} (${user.userId}) - Anonymous: ${user.anon}`);
        }

        console.log(`${this.debugPrefix} Total spectators (excluding self): ${spectatorCount}`);
        console.log(`${this.debugPrefix} Current mode: ${this.mode}`);

        if (this.mode === 'controller') {
            console.log(`${this.debugPrefix} Controller mode: checking if should create peer connections`);
            if (users.length > 1) {
                console.log(`${this.debugPrefix} Multiple users present, creating peer connections for spectators`);
                for (let userId in this.spectators) {
                    console.log(`${this.debugPrefix} Creating peer connection for spectator: ${userId}`);
                    let peer = this.controllerSpectatorPeers[userId] = await this.createSpectatorPeer(userId);
                    this.startOffer(peer, (d) => this.publicSend('signal', userId, d))
                }
            } else {
                console.log(`${this.debugPrefix} Only controller present, no spectators to connect to`);
            }
        } else {
            console.log(`${this.debugPrefix} Spectator mode: creating peer connection to receive stream`);
            this.spectatorPeer = await this.createSpectatorPeer();
        }

        console.groupEnd();
    }

    private async replaceTrack(newStream: MediaStream) {
        console.group(`${this.debugPrefix} REPLACING TRACK`);
        console.log(`${this.debugPrefix} Replacing track with new stream:`, {
            streamId: newStream.id,
            active: newStream.active,
            trackCount: newStream.getTracks().length
        });

        let videoTrack = newStream.getVideoTracks()[0];
        if (!videoTrack) {
            console.error(`${this.debugPrefix} No video track found in new stream`);
            console.groupEnd();
            return;
        }

        console.log(`${this.debugPrefix} New video track:`, {
            id: videoTrack.id,
            kind: videoTrack.kind,
            enabled: videoTrack.enabled,
            readyState: videoTrack.readyState
        });

        this.stream = newStream;

        const peerCount = Object.keys(this.controllerSpectatorPeers).length;
        console.log(`${this.debugPrefix} Updating ${peerCount} peer connections`);

        for (let controllerSpectatorPeersKey in this.controllerSpectatorPeers) {
            const controlPeer = this.controllerSpectatorPeers[controllerSpectatorPeersKey];
            console.log(`${this.debugPrefix} Updating peer: ${controllerSpectatorPeersKey}`);

            for (const sender of controlPeer.getSenders()) {
                if (sender.track && sender.track !== videoTrack) {
                    console.log(`${this.debugPrefix} Replacing track for sender`);
                    await sender.replaceTrack(videoTrack);
                }
            }
        }

        console.log(`${this.debugPrefix} Track replacement complete`);
        console.groupEnd();
    }

    private async spectatorControllerJoining(user: USER) {
        console.group(`${this.debugPrefix} PRESENCE CHANNEL - JOINING EVENT`);
        console.log(`${this.debugPrefix} User joining:`, {
            raw: user,
            type: typeof user,
            keys: user ? Object.keys(user) : 'null',
            userId: user?.userId,
            name: user?.name,
            anon: user?.anon,
            isSelf: user?.userId === this.userId
        });

        if (!user) {
            console.error(`${this.debugPrefix} User data is null in joining event`);
            console.groupEnd();
            return;
        }

        if (!user.userId) {
            console.error(`${this.debugPrefix} User data is missing userId:`, user);
            console.groupEnd();
            return;
        }

        if (user.userId === this.userId) {
            console.log(`${this.debugPrefix} User joining is self, ignoring`);
            console.groupEnd();
            return;
        }

        console.log(`${this.debugPrefix} Processing joining user: ${user.name} (${user.userId}) - Anonymous: ${user.anon}`);

        if (this.mode === 'controller') {
            console.log(`${this.debugPrefix} Controller mode: creating peer connection for new spectator`);
            let peer = this.controllerSpectatorPeers[user.userId] = await this.createSpectatorPeer(user.userId);
            console.log(`${this.debugPrefix} Starting offer for spectator: ${user.userId}`);
            this.startOffer(peer, (d) => this.publicSend('signal', user.userId, d));
        } else {
            console.log(`${this.debugPrefix} Spectator mode: checking if should create peer connection`);
            if (this.spectatorPeer == null) {
                console.log(`${this.debugPrefix} No existing spectator peer, creating new one for user: ${user.userId}`);
                this.spectatorPeer = await this.createSpectatorPeer();
            } else {
                console.log(`${this.debugPrefix} Spectator peer already exists, not creating new one`);
            }
        }

        console.groupEnd();
    }

    private spectatorControllerLeaving(user: USER) {
        console.group(`${this.debugPrefix} PRESENCE CHANNEL - LEAVING EVENT`);
        console.log(`${this.debugPrefix} User leaving:`, {
            userId: user.userId,
            name: user.name,
            anon: user.anon,
            isSelf: user.userId === this.userId
        });

        if (this.mode === 'controller') {
            console.log(`${this.debugPrefix} Controller mode: cleaning up peer connection for spectator: ${user.userId}`);
            if (this.controllerSpectatorPeers[user.userId]) {
                this.controllerSpectatorPeers[user.userId].close();
                delete this.controllerSpectatorPeers[user.userId];
                console.log(`${this.debugPrefix} Peer connection closed and removed for: ${user.userId}`);
            } else {
                console.log(`${this.debugPrefix} No peer connection found for leaving user: ${user.userId}`);
            }
        } else {
            console.log(`${this.debugPrefix} Spectator mode: user left, keeping connection for now`);
            // For spectators, we might want to close the peer if the controller leaves
            // For now, let's keep the connection and let it timeout naturally
        }

        console.groupEnd();
    }

    private spectatorControllerError(error: unknown) {
        console.group(`${this.debugPrefix} PRESENCE CHANNEL - ERROR EVENT`);
        console.error(`${this.debugPrefix} Presence channel error:`, error);
        console.groupEnd();
    }

    private async createSpectatorPeer(targetUser : number | string | null = null) {
        console.group(`${this.debugPrefix} CREATING PEER CONNECTION`);
        console.log(`${this.debugPrefix} Creating peer for target: ${targetUser}, Mode: ${this.mode}`);

        // Ensure we have dynamic credentials before creating peer connection
        if (dynamicIceServers.length === 0) {
            console.log(`${this.debugPrefix} Dynamic credentials not ready, fetching...`);
            dynamicIceServers = await getCachedWebRTCCredentials();
        }

        let peer = new RTCPeerConnection({
            iceServers: dynamicIceServers
        });

        console.log(`${this.debugPrefix} RTCPeerConnection created with ICE servers:`, dynamicIceServers);

        // Add comprehensive connection state change listeners
        peer.onconnectionstatechange = (e) => {
            console.log(`${this.debugPrefix} [${targetUser}] Connection state: ${peer.connectionState}`);
        };

        peer.oniceconnectionstatechange = (e) => {
            console.log(`${this.debugPrefix} [${targetUser}] ICE connection state: ${peer.iceConnectionState}`);
        };

        peer.onicegatheringstatechange = (e) => {
            console.log(`${this.debugPrefix} [${targetUser}] ICE gathering state: ${peer.iceGatheringState}`);
        };

        peer.onicecandidateerror = (e) => {
            console.error(`${this.debugPrefix} [${targetUser}] ICE candidate error:`, e);
        };

        peer.onsignalingstatechange = (e) => {
            console.log(`${this.debugPrefix} [${targetUser}] Signaling state: ${peer.signalingState}`);
        };

        peer.onicecandidate = this.privateGotLocalIceCandidateOffer(targetUser);
        peer.ontrack = this.privateGotTrack.bind(this);
        peer.onnegotiationneeded = (e) => {
            console.log(`${this.debugPrefix} [${targetUser}] Negotiation needed event triggered`);
        };

        // Handle stream tracks
        if (this.stream) {
            console.log(`${this.debugPrefix} Adding tracks from stream to peer. Stream ID: ${this.stream.id}, Track count: ${this.stream.getTracks().length}`);
            this.stream.getTracks().forEach((track, index) => {
                console.log(`${this.debugPrefix} Adding track ${index + 1}: ${track.kind} (${track.id}) - enabled: ${track.enabled}, readyState: ${track.readyState}`);
                peer.addTrack(track, this.stream!);
            });
        } else {
            console.log(`${this.debugPrefix} No stream available to add tracks from`);
        }

        console.log(`${this.debugPrefix} Peer connection setup complete for target: ${targetUser}`);
        console.groupEnd();
        return peer;
    }

    private privateGotLocalIceCandidateOffer(targetUser : number | string | null = null) {
        return (event: RTCPeerConnectionIceEvent) => {
            if ('candidate' in event && event.candidate) {
                console.log(`${this.debugPrefix} [${targetUser}] Sending ICE candidate:`, {
                    candidate: event.candidate.candidate,
                    sdpMLineIndex: event.candidate.sdpMLineIndex,
                    sdpMid: event.candidate.sdpMid
                });
                this.publicSend('signal', targetUser, {candidate: event.candidate, from: this.userId});
            } else if (event.candidate === null) {
                console.log(`${this.debugPrefix} [${targetUser}] ICE gathering complete (null candidate)`);
            }
        }
    }

    private privateGotTrack(event: RTCTrackEvent) {
        console.group(`${this.debugPrefix} RECEIVED TRACK EVENT`);
        console.log(`${this.debugPrefix} Track event received:`, {
            track: {
                kind: event.track.kind,
                id: event.track.id,
                enabled: event.track.enabled,
                readyState: event.track.readyState,
                muted: event.track.muted
            },
            streamCount: event.streams.length,
            receiver: event.receiver ? 'present' : 'missing',
            transceiver: event.transceiver ? 'present' : 'missing'
        });

        if (event.streams.length > 0) {
            const stream = event.streams[0];
            console.log(`${this.debugPrefix} Stream details:`, {
                id: stream.id,
                active: stream.active,
                tracks: stream.getTracks().map(t => ({
                    kind: t.kind,
                    id: t.id,
                    enabled: t.enabled,
                    readyState: t.readyState
                }))
            });

            if (this.videoElement) {
                console.log(`${this.debugPrefix} Setting video element srcObject`);
                this.videoElement.srcObject = stream;

                // Enhanced video element event listeners
                this.videoElement.onloadedmetadata = () => {
                    console.log(`${this.debugPrefix} Video metadata loaded:`, {
                        dimensions: `${this.videoElement?.videoWidth}x${this.videoElement?.videoHeight}`,
                        duration: this.videoElement?.duration,
                        readyState: this.videoElement?.readyState
                    });
                };

                this.videoElement.onplay = () => {
                    console.log(`${this.debugPrefix} Video started playing`);
                };

                this.videoElement.onpause = () => {
                    console.log(`${this.debugPrefix} Video paused`);
                };

                this.videoElement.onerror = (e) => {
                    console.error(`${this.debugPrefix} Video element error:`, e);
                };

                this.videoElement.onloadstart = () => {
                    console.log(`${this.debugPrefix} Video load started`);
                };

                this.videoElement.oncanplay = () => {
                    console.log(`${this.debugPrefix} Video can start playing`);
                };
            } else {
                console.warn(`${this.debugPrefix} No video element available to display the stream`);
            }
        } else {
            console.warn(`${this.debugPrefix} No streams available in the track event`);
        }

        console.groupEnd();
    }

    private publicSend(event: 'signal', to: number | string | null, data: ExpandedSignalData) {
        const channelName = to === null ? event : `${event}.${to}`;
        console.log(`${this.debugPrefix} Sending signal via whisper to '${channelName}':`, {
            type: 'offer' in data ? 'offer' : 'answer' in data ? 'answer' : 'candidate' in data ? 'candidate' : 'unknown',
            from: data.from,
            to: to,
            dataKeys: Object.keys(data)
        });

        if (to === null) {
            this.spectatorEcho.whisper(`${event}`, data);
        } else {
            this.spectatorEcho.whisper(`${event}.${to}`, data);
        }
    }

    private listenControllerSpectatorSignalWhisper(data: ExpandedSignalData) {
        console.log(`${this.debugPrefix} Received spectator signal:`, {
            type: 'offer' in data ? 'offer' : 'answer' in data ? 'answer' : 'candidate' in data ? 'candidate' : 'unknown',
            from: data.from,
            dataKeys: Object.keys(data)
        });

        if (!this.spectatorPeer) {
            console.error(`${this.debugPrefix} No spectator peer available to handle signal`);
            return;
        }

        this.unifiedWhisperListener(data, null, this.spectatorPeer);
    }

    private async listenControllerMachineSignalWhisper(data: ExpandedSignalData) {
        console.log(`${this.debugPrefix} Received controller signal:`, {
            type: 'offer' in data ? 'offer' : 'answer' in data ? 'answer' : 'candidate' in data ? 'candidate' : 'unknown',
            from: data.from,
            dataKeys: Object.keys(data)
        });

        let userId = data.from;
        let peer = this.controllerSpectatorPeers[userId];

        if (!peer) {
            console.log(`${this.debugPrefix} Creating new peer for user: ${userId}`);
            peer = this.controllerSpectatorPeers[userId] = await this.createSpectatorPeer(userId);
        }

        this.unifiedWhisperListener(data, userId, peer);
    }

    private async unifiedWhisperListener(data: ExpandedSignalData, userId: number | string | null, peer: RTCPeerConnection) {
        console.group(`${this.debugPrefix} PROCESSING WEBRTC SIGNAL`);

        try {
            if ('offer' in data) {
                console.log(`${this.debugPrefix} Processing offer from ${data.from}`);
                console.log(`${this.debugPrefix} Peer state before offer:`, {
                    signalingState: peer.signalingState,
                    iceConnectionState: peer.iceConnectionState,
                    connectionState: peer.connectionState
                });

                await peer.setRemoteDescription(new RTCSessionDescription(data.offer));
                console.log(`${this.debugPrefix} Remote description set, creating answer`);

                const answer = await peer.createAnswer();
                await peer.setLocalDescription(answer);
                console.log(`${this.debugPrefix} Answer created and local description set`);

                this.publicSend('signal', userId, {answer, from: this.userId});

            } else if ('answer' in data) {
                console.log(`${this.debugPrefix} Processing answer from ${data.from}`);
                console.log(`${this.debugPrefix} Peer state before answer:`, {
                    signalingState: peer.signalingState,
                    iceConnectionState: peer.iceConnectionState,
                    connectionState: peer.connectionState
                });

                await peer.setRemoteDescription(new RTCSessionDescription(data.answer));
                console.log(`${this.debugPrefix} Answer processed successfully`);

            } else if ('candidate' in data && data.candidate) {
                console.log(`${this.debugPrefix} Processing ICE candidate from ${data.from}`);

                if (peer.remoteDescription) {
                    await peer.addIceCandidate(new RTCIceCandidate(data.candidate));
                    console.log(`${this.debugPrefix} ICE candidate added successfully`);
                } else {
                    console.warn(`${this.debugPrefix} Cannot add ICE candidate - no remote description set yet`);
                }
            } else {
                console.warn(`${this.debugPrefix} Unknown signal type received:`, Object.keys(data));
            }
        } catch (error) {
            console.error(`${this.debugPrefix} Error processing WebRTC signal:`, error);
        }

        console.groupEnd();
    }

    private async startOffer(peer: RTCPeerConnection, send: (d: { offer: any, from: number | string }) => void) {
        console.group(`${this.debugPrefix} CREATING WEBRTC OFFER`);

        try {
            console.log(`${this.debugPrefix} Creating offer...`);
            console.log(`${this.debugPrefix} Peer state before offer:`, {
                signalingState: peer.signalingState,
                iceConnectionState: peer.iceConnectionState,
                connectionState: peer.connectionState
            });

            const offer = await peer.createOffer();
            console.log(`${this.debugPrefix} Offer created, setting local description`);

            await peer.setLocalDescription(offer);
            console.log(`${this.debugPrefix} Local description set, sending offer`);

            send({offer, from: this.userId});
            console.log(`${this.debugPrefix} Offer sent successfully`);

        } catch (error) {
            console.error(`${this.debugPrefix} Error creating/sending offer:`, error);
        }

        console.groupEnd();
    }

    public leaving() {
        console.log(`${this.debugPrefix} Leaving presence channel: machineSpectator.${this.machineId}`);
        window.Echo.leave(`machineSpectator.${this.machineId}`);

        // Clean up peer connections
        if (this.spectatorPeer) {
            console.log(`${this.debugPrefix} Closing spectator peer connection`);
            this.spectatorPeer.close();
            this.spectatorPeer = null;
        }

        Object.keys(this.controllerSpectatorPeers).forEach(userId => {
            console.log(`${this.debugPrefix} Closing controller peer connection for user: ${userId}`);
            this.controllerSpectatorPeers[userId].close();
            delete this.controllerSpectatorPeers[userId];
        });

        console.log(`${this.debugPrefix} Cleanup complete`);
    }
}
