/**
 * WebRTC Utilities for dynamic STUN/TURN server credential management
 */

export interface WebRTCCredentials {
    username: string | null;
    password: string | null;
    ttl: number;
    urls: string[];
    realm: string | null;
}

/**
 * Fetch dynamic STUN/TURN server credentials from the backend
 * @returns Promise<RTCIceServer[]> Array of ICE servers for WebRTC configuration
 */
export async function fetchWebRTCCredentials(): Promise<RTCIceServer[]> {
    try {
        console.log('[WebRTC Utils] Fetching dynamic credentials from backend...');
        
        const response = await fetch('/api/webrtc/credentials', {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
            },
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch WebRTC credentials: ${response.status} ${response.statusText}`);
        }

        const credentials: WebRTCCredentials = await response.json();
        console.log('[WebRTC Utils] Received credentials:', {
            username: credentials.username ? 'present' : 'null',
            password: credentials.password ? 'present' : 'null',
            ttl: credentials.ttl,
            urls: credentials.urls,
            realm: credentials.realm
        });

        // Convert credentials to RTCIceServer format
        const iceServers: RTCIceServer[] = [];

        if (credentials.urls && credentials.urls.length > 0) {
            credentials.urls.forEach(url => {
                if (url.startsWith('stun:')) {
                    // STUN servers don't need credentials
                    iceServers.push({ urls: url });
                } else if (url.startsWith('turn:') && credentials.username && credentials.password) {
                    // TURN servers need credentials
                    iceServers.push({
                        urls: url,
                        username: credentials.username,
                        credential: credentials.password
                    });
                }
            });
        }

        console.log('[WebRTC Utils] Generated ICE servers:', iceServers);
        return iceServers;

    } catch (error) {
        console.error('[WebRTC Utils] Error fetching credentials:', error);
        
        // Fallback to hardcoded STUN server if dynamic fetch fails
        console.warn('[WebRTC Utils] Falling back to hardcoded STUN server');
        return [
            { urls: 'stun:stun.l.google.com:19302' }
        ];
    }
}

/**
 * Cache for WebRTC credentials to avoid repeated API calls
 */
let credentialsCache: {
    iceServers: RTCIceServer[];
    expiresAt: number;
} | null = null;

/**
 * Get WebRTC credentials with caching support
 * @param forceRefresh Force refresh of cached credentials
 * @returns Promise<RTCIceServer[]> Array of ICE servers for WebRTC configuration
 */
export async function getCachedWebRTCCredentials(forceRefresh: boolean = false): Promise<RTCIceServer[]> {
    const now = Date.now();
    
    // Check if we have valid cached credentials
    if (!forceRefresh && credentialsCache && credentialsCache.expiresAt > now) {
        console.log('[WebRTC Utils] Using cached credentials');
        return credentialsCache.iceServers;
    }

    // Fetch new credentials
    const iceServers = await fetchWebRTCCredentials();
    
    // Cache credentials for 10 minutes (shorter than server TTL for safety)
    credentialsCache = {
        iceServers,
        expiresAt: now + (10 * 60 * 1000) // 10 minutes
    };

    return iceServers;
}
