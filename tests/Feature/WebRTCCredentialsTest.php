<?php

namespace Tests\Feature;

use Tests\TestCase;

class WebRTCCredentialsTest extends TestCase
{

    public function test_webrtc_credentials_endpoint_returns_valid_response()
    {
        $response = $this->get('/api/webrtc/credentials');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'username',
            'password',
            'ttl',
            'urls',
            'realm',
        ]);
    }

    public function test_webrtc_credentials_with_authenticated_user()
    {
        // Skip this test if database is not available
        $this->markTestSkipped('Database not available in test environment');
    }

    public function test_webrtc_credentials_with_guest_user()
    {
        $response = $this->withSession(['_token' => 'test-token'])
                        ->get('/api/webrtc/credentials');

        $response->assertStatus(200);
        $data = $response->json();
        
        // Check that username contains guest prefix when not authenticated
        if ($data['username']) {
            $this->assertStringContainsString('guest_', $data['username']);
        }
    }

    public function test_webrtc_credentials_without_turn_secret()
    {
        // Temporarily unset TURN secret to test STUN-only mode
        config(['services.turn.secret' => null]);

        $response = $this->get('/api/webrtc/credentials');

        $response->assertStatus(200);
        $data = $response->json();
        
        // Should return null credentials for TURN servers
        $this->assertNull($data['username']);
        $this->assertNull($data['password']);
        
        // Should only return STUN URLs
        $stunUrls = array_filter($data['urls'], fn($url) => str_starts_with($url, 'stun:'));
        $this->assertNotEmpty($stunUrls);
    }
}
